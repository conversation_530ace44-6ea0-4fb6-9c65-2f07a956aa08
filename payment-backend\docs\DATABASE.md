# 数据库配置和使用指南

## 概述

支付后端服务使用MySQL数据库作为唯一的数据存储方案，确保开发、测试和生产环境的一致性。

## 架构优势

- **环境一致性**: 开发、测试、生产环境都使用相同的数据库引擎
- **简化维护**: 移除了内存存储实现，减少代码复杂度
- **真实测试**: 开发环境使用真实数据库，更好地发现潜在问题
- **连接池管理**: 统一的连接池配置和管理
- **事务支持**: 完整的ACID事务支持

## 配置

### MySQL数据库配置

在配置文件中设置：
```yaml
database:
  driver: "mysql"
  host: "localhost"
  port: 3306
  username: "payment_user"
  password: "payment_password"
  database: "payment_db"
  ssl_mode: "disable"
```

或通过环境变量：
```bash
export PAYMENT_DATABASE_DRIVER=mysql
export PAYMENT_DATABASE_HOST=localhost
export PAYMENT_DATABASE_PORT=3306
export PAYMENT_DATABASE_USERNAME=payment_user
export PAYMENT_DATABASE_PASSWORD=payment_password
export PAYMENT_DATABASE_DATABASE=payment_db
```

## 数据库表结构

### payments 表
- `id` - UUID主键
- `session_id` - 会话ID（索引）
- `customer_id` - 客户ID（索引）
- `amount` - 金额（以分为单位）
- `currency` - 货币类型
- `status` - 支付状态（索引）
- `provider` - 支付提供商
- `provider_payment_id` - 提供商支付ID（索引）
- `description` - 描述
- `metadata` - JSON元数据
- `created_at` - 创建时间
- `updated_at` - 更新时间
- `completed_at` - 完成时间

### checkout_sessions 表
- `id` - 字符串主键
- `payment_id` - 关联的支付ID（索引）
- `customer_id` - 客户ID（索引）
- `amount` - 金额
- `currency` - 货币类型
- `provider` - 支付提供商
- `success_url` - 成功回调URL
- `cancel_url` - 取消回调URL
- `description` - 描述
- `metadata` - JSON元数据
- `expires_at` - 过期时间（索引）
- `created_at` - 创建时间

### checkout_session_items 表
- `id` - 自增主键
- `session_id` - 会话ID（外键，索引）
- `name` - 项目名称
- `description` - 项目描述
- `quantity` - 数量
- `unit_amount` - 单价（以分为单位）

## 数据库迁移

### 使用迁移脚本

```bash
# 运行数据库迁移
go run scripts/migrate.go -action=migrate

# 删除所有表
go run scripts/migrate.go -action=drop

# 查看数据库信息
go run scripts/migrate.go -action=info

# 使用指定配置文件
go run scripts/migrate.go -config=configs/config.prod.yaml -action=migrate
```

### 手动迁移

应用启动时会自动执行数据库迁移，包括：
1. 创建表结构
2. 创建索引
3. 设置外键约束

## 连接池配置

数据库连接池配置在 `internal/db/db.go` 中：

```go
// 配置连接池
sqlDB.SetMaxIdleConns(10)           // 最大空闲连接数
sqlDB.SetMaxOpenConns(100)          // 最大打开连接数
sqlDB.SetConnMaxLifetime(30 * time.Minute) // 连接最大生存时间
```

## 事务支持

### 使用事务包装器

```go
err := db.WithTransaction(func(tx *gorm.DB) error {
    // 在事务中执行操作
    if err := paymentRepo.CreateWithTransaction(tx, payment); err != nil {
        return err
    }
    
    if err := sessionRepo.CreateWithTransaction(tx, session); err != nil {
        return err
    }
    
    return nil
})
```

### 仓储层事务方法

每个仓储都提供了事务版本的方法：
- `CreateWithTransaction`
- `UpdateWithTransaction`
- `GetByIDWithTransaction`
- `DeleteWithTransaction`

## 协程安全

- **GORM** 本身是协程安全的
- **连接池** 自动管理并发连接
- **事务** 在单个协程中执行，避免竞态条件
- **仓储实现** 无状态，可安全并发使用

## 性能优化

### 索引策略
- 主键索引：自动创建
- 外键索引：自动创建
- 业务索引：根据查询模式创建

### 查询优化
- 使用 `Preload` 预加载关联数据
- 使用 `Select` 指定需要的字段
- 使用分页避免大量数据查询

### 连接池优化
- 根据并发量调整 `MaxOpenConns`
- 根据空闲时间调整 `MaxIdleConns`
- 根据网络延迟调整 `ConnMaxLifetime`

## 监控和日志

### 数据库日志
GORM 配置为 Info 级别日志，记录：
- SQL 查询语句
- 执行时间
- 影响行数
- 错误信息

### 连接监控
可以通过以下方式监控连接池状态：
```go
sqlDB, _ := db.DB()
stats := sqlDB.Stats()
fmt.Printf("Open connections: %d\n", stats.OpenConnections)
fmt.Printf("In use: %d\n", stats.InUse)
fmt.Printf("Idle: %d\n", stats.Idle)
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查数据库服务是否运行
   - 验证连接参数
   - 检查网络连接

2. **迁移失败**
   - 检查数据库权限
   - 验证表是否已存在
   - 查看详细错误日志

3. **性能问题**
   - 检查索引使用情况
   - 监控连接池状态
   - 分析慢查询日志

### 调试技巧

1. 启用详细日志：
```go
DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
    Logger: logger.Default.LogMode(logger.Info),
})
```

2. 使用数据库工具检查表结构和数据

3. 监控应用性能指标
